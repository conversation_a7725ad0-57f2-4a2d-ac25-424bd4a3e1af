import axios from '@/plugin/axios';
import {SESSION_API} from './webApi';

import {ApiResponse, FilterSaveData, SessionQueryParams} from "@/types/analysis";

// 获取会话筛选条件配置
export function getSessionConditionConfig(): Promise<ApiResponse> {
    return axios({
        url: SESSION_API.GET_CONDITION_CONFIG,
        method: 'get',
        params: {
            // 添加必要的channel参数，固定使用online频道
            channel: 'online'
        }
    });
}

// 获取会话场景关系配置
export function getSessionConditionScene(): Promise<ApiResponse> {
    return axios({
        url: SESSION_API.GET_CONDITION_SCENE,
        method: 'get'
    });
}

// 获取会话任务版本
export function getSessionConditionTaskVersions(params: SessionQueryParams): Promise<ApiResponse> {
    return axios({
        url: SESSION_API.GET_CONDITION_TASK_VERSIONS,
        method: 'get',
        params
    });
}

// 获取会话任务节点
export function getSessionConditionTaskNodes(params: SessionQueryParams): Promise<ApiResponse> {
    return axios({
        url: SESSION_API.GET_CONDITION_TASK_NODES,
        method: 'get',
        params
    });
}

// 获取会话分页列表
export function getSessionPage(params: SessionQueryParams): Promise<ApiResponse> {
    // 确保必要的默认参数
    const defaultParams: SessionQueryParams = {
        channel: 'online', // 固定使用online频道
        pageNum: params.page || 1,
        pageSize: params.pageSize || 10
    };

    // 处理时间参数
    if (params.startTime && params.endTime) {
        defaultParams.startTime = new Date(params.startTime).getTime();
        defaultParams.endTime = new Date(params.endTime).getTime();
    } else {
        // 如果没有时间参数，默认使用最近7天
        const now = new Date();
        defaultParams.endTime = now.getTime();
        const sevenDaysAgo = new Date();
        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
        defaultParams.startTime = sevenDaysAgo.getTime();
    }

    // 合并传入的参数与默认参数
    const mergedParams: SessionQueryParams = {
        ...defaultParams,
        ...params,
        // 确保使用pageNum而不是page
        pageNum: params.page || defaultParams.pageNum
    };

    // 删除page属性，因为后端期望的是pageNum
    delete mergedParams.page;

    return axios({
        url: SESSION_API.POST_SESSION_PAGE,
        method: 'post',
        data: mergedParams
    });
}

// 获取会话详情
export function getSessionDetail(id: string): Promise<ApiResponse> {
    return axios({
        url: `${SESSION_API.GET_SESSION_DETAIL}${id}`,
        method: 'get'
    });
}

// 获取会话消息
export function getSessionMessages(id: string): Promise<ApiResponse> {
    return axios({
        url: SESSION_API.GET_SESSION_MESSAGES.replace('{id}', id),
        method: 'get'
    });
}

// 获取会话执行路径
export function getSessionExecPath(id: string): Promise<ApiResponse> {
    return axios({
        url: SESSION_API.GET_SESSION_EXEC_PATH.replace('{id}', id),
        method: 'get'
    });
}

// 获取会话信号信息
export function getSessionSignals(id: string): Promise<ApiResponse> {
    return axios({
        url: SESSION_API.GET_SESSION_SIGNALS.replace('{id}', id),
        method: 'get'
    });
}

// 收藏会话
export function favoriteSession(id: string): Promise<ApiResponse> {
    return axios({
        url: SESSION_API.FAVORITE_SESSION.replace('{id}', id),
        method: 'post'
    });
}

// 取消收藏会话
export function unfavoriteSession(id: string): Promise<ApiResponse> {
    return axios({
        url: SESSION_API.UNFAVORITE_SESSION.replace('{id}', id),
        method: 'post'
    });
}

// 获取收藏的会话列表
export function getFavoriteSessionList(): Promise<ApiResponse> {
    return axios({
        url: SESSION_API.POST_FAVORITE_SESSIONS,
        method: 'post'
    });
}

// 保存筛选条件
export function saveFilterCondition(data: FilterSaveData): Promise<ApiResponse> {
    return axios({
        url: SESSION_API.ADD_SAVED_FILTER_RECORD,
        method: 'post',
        data
    });
}

// 获取保存的筛选条件列表
export function getSavedFilterList(analysisType: string): Promise<ApiResponse> {
    return axios({
        url: SESSION_API.LIST_SAVED_FILTER_RECORDS,
        method: 'get',
        params: { analysisType }
    });
}

// 删除已保存的筛选条件
export function deleteSavedFilter(id: string): Promise<ApiResponse> {
    return axios({
        url: SESSION_API.DELETE_SAVED_FILTER_RECORD,
        method: 'post',
        data: { recordId: id }
    });
} 