package com.meituan.csc.aigc.eval.controller;

import com.meituan.csc.aigc.eval.controller.vo.Result;
import com.meituan.csc.aigc.eval.dto.PageData;
import com.meituan.csc.aigc.eval.dto.gpt.GptReplyDTO;
import com.meituan.csc.aigc.eval.dto.workbench.*;
import com.meituan.csc.aigc.eval.enums.workbench.ChannelEnum;
import com.meituan.csc.aigc.eval.param.PageParam;
import com.meituan.csc.aigc.eval.param.customconfig.LlmSelectedParam;
import com.meituan.csc.aigc.eval.param.customconfig.NodeSelectedParam;
import com.meituan.csc.aigc.eval.param.customconfig.SignalReorderParam;
import com.meituan.csc.aigc.eval.param.inspect.*;
import com.meituan.csc.aigc.eval.param.workbench.*;
import com.meituan.csc.aigc.eval.service.InspectWorkbenchExecuteService;
import com.meituan.csc.aigc.eval.utils.SessionBizUtil;
import com.sankuai.call.sdk.entity.record.QueryRecordDataDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/aigc/eval/workbench")
@Slf4j
public class InspectWorkbenchController {

    @Autowired
    private InspectWorkbenchExecuteService inspectWorkbenchExecuteService;

    @PostMapping("/session/favorites/add")
    public Result<Boolean> addSessionFavorite(@RequestBody InspectWorkbenchConditionParam condition) {
        return Result.ok(inspectWorkbenchExecuteService.addSessionFavorite(condition.getSessionId()));
    }

    @PostMapping("/session/favorites/cancel")
    public Result<Boolean> deleteSessionFavorite(@RequestBody InspectWorkbenchConditionParam condition) {
        return Result.ok(inspectWorkbenchExecuteService.deleteSessionFavorite(condition.getSessionId()));
    }

    @PostMapping("/session/favorites")
    public Result<PageData<InspectWorkbenchDTO>> pageSessionFavorite(@RequestBody InspectWorkbenchConditionParam condition) {
        long start = System.currentTimeMillis();
        PageParam<InspectWorkbenchConditionParam> pageParam = new PageParam<>();
        pageParam.setPageNum(condition.getPageNum());
        pageParam.setPageSize(condition.getPageSize());
        pageParam.setCondition(condition);
        PageData<InspectWorkbenchDTO> result = inspectWorkbenchExecuteService.pageSessionNew(pageParam);
        long milliseconds = System.currentTimeMillis() - start;
        String seconds = String.format("%.2f", milliseconds / 1000.0);
        result.setCost(seconds);
        return Result.ok(result);
    }

    @PostMapping("/session/list")
    public Result<PageData<InspectWorkbenchDTO>> pageSessionNew(@RequestBody InspectWorkbenchConditionParam condition) {
        long start = System.currentTimeMillis();
        PageParam<InspectWorkbenchConditionParam> pageParam = new PageParam<>();
        pageParam.setPageNum(condition.getPageNum());
        pageParam.setPageSize(condition.getPageSize());
        pageParam.setCondition(condition);
        PageData<InspectWorkbenchDTO> result = inspectWorkbenchExecuteService.pageSessionNew(pageParam);
        long milliseconds = System.currentTimeMillis() - start;
        String seconds = String.format("%.2f", milliseconds / 1000.0);
        result.setCost(seconds);
        return Result.ok(result);
    }

    @PostMapping("/session/page")
    public Result<PageData<InspectWorkbenchDTO>> pageSessionPost(@RequestBody InspectWorkbenchConditionParam condition) {
        long start = System.currentTimeMillis();
        PageParam<InspectWorkbenchConditionParam> pageParam = new PageParam<>();
        pageParam.setPageNum(condition.getPageNum());
        pageParam.setPageSize(condition.getPageSize());
        pageParam.setCondition(condition);
        PageData<InspectWorkbenchDTO> result = inspectWorkbenchExecuteService.pageSession(pageParam);
        long milliseconds = System.currentTimeMillis() - start;
        String seconds = String.format("%.2f", milliseconds / 1000.0);
        result.setCost(seconds);
        return Result.ok(result);
    }

    @Deprecated
    @GetMapping("/session/detail")
    public Result<List<InspectWorkbenchSessionDetailDTO>> sessionDetail(@RequestParam("sessionId") String sessionId,
                                                                        @RequestParam("platformType") Integer platformType,
                                                                        @RequestParam("llmSessionId") List<String> llmSessionIdList) {
        inspectWorkbenchExecuteService.saveLog(sessionId);
        return Result.ok(inspectWorkbenchExecuteService.sessionDetail(sessionId, platformType, llmSessionIdList));
    }

    /**
     * 获取对话详情
     *
     * @param param 入参
     * @return 对话详情列表
     */
    @PostMapping("/session/detail")
    public Result<List<InspectWorkbenchSessionDetailDTO>> sessionDetail(@RequestBody InspectWorkbenchSessionDetailParam param) {
        // 前置检查入参
        SessionBizUtil.InspectWorkbenchSessionDetailParamCheck(param);

        String sessionId = param.getSessionId();
        Integer platformType = param.getPlatformType();
        List<String> llmSessionIdList = param.getLlmSessionIdList();

        inspectWorkbenchExecuteService.saveLog(sessionId);
        return Result.ok(inspectWorkbenchExecuteService.sessionDetail(sessionId, platformType, llmSessionIdList));
    }

    @GetMapping("/session/overview")
    public Result<InspectWorkbenchDTO> sessionOverview(@RequestParam("sessionId") String sessionId) {
        return Result.ok(inspectWorkbenchExecuteService.sessionOverview(sessionId));
    }

    @GetMapping("/session/basic")
    public Result<InspectWorkbenchDTO> sessionBasic(@RequestParam("sessionId") String sessionId,
                                                @RequestParam("analysisType") String analysisType) {
        return Result.ok(inspectWorkbenchExecuteService.sessionBasic(sessionId, analysisType));
    }

    /**
     * 根据渠道获取会话筛选条件配置
     * @param channel
     * @return
     */
    @GetMapping("/session/condition/config")
    public Result<FilterConfigDTO> sessionConditionConfig(@RequestParam("channel") String channel) {
        return Result.ok(inspectWorkbenchExecuteService.sessionConditionConfig(channel));
    }

    /**
     * 获取场景相关的配置关联
     * @return
     */
    @GetMapping("/session/condition/scene")
    public Result<SceneRelationConfigDTO> sessionConditionSceneFilter() {
        return Result.ok(inspectWorkbenchExecuteService.sessionConditionSceneRelationConfig());
    }

    /**
     * 获取taskKey相关的版本
     * @return
     */
    @GetMapping("/session/condition/task/versions")
    public Result<TaskVersionDTO> sessionConditionTaskKeyVersion(@RequestParam("taskKey") String taskKey) {
        return Result.ok(inspectWorkbenchExecuteService.sessionConditionTaskKeyVersions(taskKey));
    }

    /**
     * 获取taskKey和版本关联的节点
     * @return
     */
    @GetMapping("/session/condition/task/nodes")
    public Result<TaskVersionNodeDTO> sessionConditionTaskKeyVersionNode(@RequestParam("taskKey") String taskKey,
                                                                         @RequestParam("taskVersion") String taskVersion) {
        return Result.ok(inspectWorkbenchExecuteService.sessionConditionTaskKeyNodes(taskKey, taskVersion));
    }

    /**
     * 获取会话筛选条件的记录列表
     * @param analysisType
     * @return
     */
    @GetMapping("/session/condition/record/list")
    public Result<List<SessionConditionRecordDTO>> sessionConditionConfigList(@RequestParam("analysisType") String analysisType) {
        return Result.ok(inspectWorkbenchExecuteService.listSessionConditionRecord(analysisType));
    }

    /**
     * 添加会话筛选条件的记录
     * @param param
     * @return
     */
    @PostMapping("/session/condition/record/add")
    public Result<Boolean> addSessionConditionRecord(@RequestBody SessionConditionRecordDTO param){
        return Result.ok(inspectWorkbenchExecuteService.addSessionConditionRecord(param));
    }

    /**
     * 删除会话筛选条件的记录
     * @param param
     * @return
     */
    @PostMapping("/session/condition/record/delete")
    public Result<?> delSessionConditionRecord(@RequestBody SessionConditionRecordDTO param){
        return Result.ok(inspectWorkbenchExecuteService.deleteSessionConditionRecord(param));
    }

    /**
     * 获取IVR详情
     *
     * @param sessionId
     * @return
     */
    @GetMapping("/session/ivr/info")
    public Result<String> sessionIvrInfo(@RequestParam("sessionId") String sessionId) {
        return Result.ok(inspectWorkbenchExecuteService.sessionIvrInfo(sessionId));
    }

    /**
     * 获取外呼信息
     * @param contactId
     * @return
     */
    @GetMapping("/calls/{contactId}/info")
    public Result<List<QueryRecordDataDTO>> getCallOutInfo(@PathVariable("contactId") String contactId) {
        return Result.ok(inspectWorkbenchExecuteService.getCallOutInfo(contactId));
    }

    @PostMapping("/query/detail")
    public Result<InspectWorkbenchQueryDetailDTO> queryDetail(@RequestBody InspectWorkbenchDetailParam param) {
        inspectWorkbenchExecuteService.saveLog(param);
        // 从前端来的请求要对比信号变化
        param.setIsCompareOldValue(true);
        return Result.ok(inspectWorkbenchExecuteService.queryDetail(param));
    }

    /***
     * 查询模型调试信息 树形结构
     * @param param
     * @return
     */
    @PostMapping("/query/llm/tree")
    public Result<InspectWorkbenchQueryLlmTreeDTO> queryLlmTree(@RequestBody InspectWorkbenchDetailParam param) {
        return Result.ok(inspectWorkbenchExecuteService.queryLlmTree(param));
    }

    /***
     * 变更信号排序接口
     * @param param
     * @return
     */
    @PutMapping("/signal/reorder")
    public Result<?> signalReorder(@RequestBody SignalReorderParam param) {
        inspectWorkbenchExecuteService.signalReorder(param);
        return Result.create();
    }

    /***
     * 变更节点选择接口
     * @param param
     * @return
     */
    @PutMapping("/node/selected")
    public Result<?> nodeSelected(@RequestBody NodeSelectedParam param) {
        inspectWorkbenchExecuteService.nodeSelected(param);
        return Result.create();
    }

    /***
     * 变更模型选择接口
     * @param param
     * @return
     */
    @PutMapping("/llm/selected")
    public Result<?> llmSelected(@RequestBody LlmSelectedParam param) {
        inspectWorkbenchExecuteService.llmSelected(param);
        return Result.create();
    }

    /***
     * 调试模型节点
     * @param param
     * @return
     */
    @PostMapping("/llm/debug")
    public Result<GptReplyDTO> llmDebug(@RequestBody WorkbenchLlmDebugParam param) {
        GptReplyDTO gptReplyDTO = inspectWorkbenchExecuteService.llmDebug(param);
        return Result.ok(gptReplyDTO);
    }

    @PostMapping("/query/detail/path")
    public Result<?> queryDetailPath(@RequestBody InspectWorkbenchDetailPathParam param) {
        return Result.ok(inspectWorkbenchExecuteService.queryDetailPath(param));
    }

    @PostMapping("/query/inspect")
    public Result<?> queryInspect(@RequestBody InspectWorkbenchAgreeParam param) {
        inspectWorkbenchExecuteService.queryInspect(param);
        return Result.create();
    }

    /**
     * 加入错题集
     */
    @PostMapping("/query/collect")
    public Result<?> queryCollect(@RequestBody InspectWorkbenchCollectParam param) {
        inspectWorkbenchExecuteService.queryCollect(param);
        return Result.create();
    }


    /**
     * 查询人工质检
     *
     * @param dimension     质检维度 0-Session维度 1-Query维度
     * @param sessionId     会话ID
     * @param messageId     消息ID （可选）
     * @param applicationId 应用ID
     * @return 质检结果
     */
    @GetMapping("/manual/inspect")
    public Result<List<ManualInspectResultDTO>> getManualInspectResult(
            @RequestParam("dimension") Integer dimension,
            @RequestParam("sessionId") String sessionId,
            @RequestParam(value = "messageId", required = false) String messageId,
            @RequestParam(value = "llmMessageId", required = false) String llmMessageId,
            @RequestParam(value = "applicationId", required = false) String applicationId) {
        List<ManualInspectResultDTO> result = inspectWorkbenchExecuteService.getManualInspectResult(dimension, applicationId, sessionId, messageId, llmMessageId);
        return Result.ok(result);
    }


    /**
     * 提交人工质检结果
     *
     * @param param 质检结果
     * @return 提交结果
     */
    @PostMapping("/manual/inspect")
    public Result<?> submitManualInspectResult(@RequestBody ManualInspectParam param) {
        log.info("Received manual inspect submission: {}", param);
        inspectWorkbenchExecuteService.submitManualInspectResult(param);
        return Result.create();
    }

    /**
     * 采纳机器质检结果
     *
     * @param param 质检结果
     * @return 采纳结果
     */
    @PostMapping("/manual/inspect/adopt")
    public Result<?> adoptAutoInspectResult(@RequestBody WorkbenchAdoptParam param) {
        inspectWorkbenchExecuteService.adoptAutoInspectResult(param);
        return Result.create();
    }

    /**
     * 获取机器人工指标映射
     *
     * @return 指标映射
     */
    @GetMapping("/metrics/mapping")
    public Result<List<WorkbenchMetricsMappingDTO>> metricsMapping() {
        return Result.ok(inspectWorkbenchExecuteService.getWorkbenchMetricsMappingDTOList());
    }

    /**
     * 批量获取会话详情
     *
     * @param sessionIdList 会话ID列表
     * @return 会话详情列表
     */
    @PostMapping("/session/info/batch")
    public Result<List<WorkbenchAutoInspectDTO>> sessionDetailInfoBatch(@RequestBody List<String> sessionIdList) {
        return Result.ok(inspectWorkbenchExecuteService.getSessionDetailInfoBatch(sessionIdList));
    }

    /**
     * 获取可选择的工作空间和APP
     *
     * @return 工作空间和APP列表
     */
    @GetMapping("/workspaces/apps")
    public Result<List<WorkspaceAppDTO>> getWorkspacesAndApps() {
        // 注意：现在只有电话会话分析走这个接口
        List<WorkspaceAppDTO> workspaceAppList = inspectWorkbenchExecuteService.getWorkspacesAndAppsBySceneType(ChannelEnum.IVR.getCode());
        return Result.ok(workspaceAppList);
    }


    /**
     * 会话导出
     *
     * @param workspaceId      工作空间ID
     * @param applicationId    应用ID
     * @param sessionId        会话ID
     * @param startTime        开始时间
     * @param endTime          结束时间
     * @param userId           用户ID
     * @param orderId          订单ID
     * @param inspectionStatus 质检状态
     * @return 导出任务提交状态
     */
    @GetMapping("/session/export")
    public Result<?> exportSession(@RequestParam(value = "workspaceId", required = false) String workspaceId,
                                   @RequestParam(value = "applicationId", required = false) String applicationId,
                                   @RequestParam(value = "appVersion", required = false) String appVersionId,
                                   @RequestParam(value = "sessionId", required = false) String sessionId,
                                   @RequestParam(value = "startTime", required = false) Long startTime,
                                   @RequestParam(value = "endTime", required = false) Long endTime,
                                   @RequestParam(value = "userId", required = false) String userId,
                                   @RequestParam(value = "orderId", required = false) String orderId,
                                   @RequestParam(value = "inspectionStatus", required = false) Integer inspectionStatus,
                                   @RequestParam(value = "visitId", required = false) String visitId,
                                   @RequestParam(value = "questionName", required = false) String questionName,
                                   @RequestParam(value = "sessionSolved", required = false) String sessionSolved,
                                   @RequestParam(value = "isTimeoutEnd", required = false) Boolean isTimeoutEnd,
                                   @RequestParam(value = "isExceptionEnd", required = false) Boolean isExceptionEnd,
                                   @RequestParam(value = "channel", required = false) String channel,
                                   @RequestParam(value = "scene", required = false) String scene,
                                   @RequestParam(value = "bu", required = false) String bu,
                                   @RequestParam(value = "subBu", required = false) String subBu,
                                   @RequestParam(value = "stars", required = false)  String  stars,
                                   @RequestParam(value = "transferStaff", required = false) List<String> transferStaff,
                                   @RequestParam(value = "taskKey", required = false) String taskKey,
                                   @RequestParam(value = "taskVersion", required = false) String taskVersion,
                                   @RequestParam(value = "taskNode", required = false) String taskNode
    ) {
        PageParam<InspectWorkbenchConditionParam> condition = buildCondition(workspaceId, applicationId, appVersionId,
                sessionId, startTime, endTime, userId, orderId, inspectionStatus, visitId, questionName, sessionSolved,
                isTimeoutEnd, isExceptionEnd, channel, scene, bu, subBu, stars, transferStaff,  taskKey, taskVersion, taskNode,
                0, 0);
        inspectWorkbenchExecuteService.exportSession(condition.getCondition());
        return Result.create();
    }

    @PostMapping("/session/export")
    public Result<PageData<InspectWorkbenchDTO>> export(@RequestBody InspectWorkbenchConditionParam condition) {
        inspectWorkbenchExecuteService.exportSession(condition);
        return Result.create();
    }

    /**
     * 获取当前用户角色
     *
     * @return 角色枚举
     */
    @GetMapping("/role")
    public Result<WorkbenchRoleDTO> getRole() {
        return Result.ok(inspectWorkbenchExecuteService.getRole());
    }

    /**
     * 保存参考调试窗口宽度设置
     */
    @PutMapping("/window-width/save")
    public Result<?> saveReferenceDebuggingWindowWidth(@RequestBody ReferenceDebuggingWindowWidthSetUpParam param){
        inspectWorkbenchExecuteService.saveReferenceDebuggingWindowWidth(param.getTotalWidth(), param.getReferenceWindowWidth());
        return Result.create();
    }

    private PageParam<InspectWorkbenchConditionParam> buildCondition(String workspaceId, String applicationId, String appVersionId,
                                                                     String sessionId, Long startTime, Long endTime, String userId,
                                                                     String orderId, Integer inspectionStatus, String visitId,
                                                                     String questionName, String sessionSolved,
                                                                     Boolean isTimeoutEnd, Boolean isExceptionEnd, String channel,
                                                                     String scene, String bu, String subBu, String stars, List<String> transferStaff,
                                                                     String taskKey, String taskVersion, String taskNode,
                                                                     int pageNum, int pageSize) {
        InspectWorkbenchConditionParam condition = new InspectWorkbenchConditionParam();
        condition.setSessionId(sessionId);
        if (startTime != null) {
            condition.setStartTime(new Date(startTime));
        }
        if (endTime != null) {
            condition.setEndTime(new Date(endTime));
        }
        condition.setUserId(userId);
        condition.setOrderId(orderId);
        condition.setWorkspaceId(workspaceId);
        condition.setApplicationId(applicationId);
        condition.setApplicationVersionId(appVersionId);
        condition.setInspectionStatus(inspectionStatus);
        condition.setSessionSolved(sessionSolved);
        // 设置新增的参数
        condition.setVisitId(visitId);
        condition.setQuestionName(questionName);
        condition.setIsTimeoutEnd(isTimeoutEnd);
        condition.setIsExceptionEnd(isExceptionEnd);
        condition.setChannel(channel);
        condition.setScene(scene);
        condition.setBu(bu);
        condition.setSubBu(subBu);
        condition.setStars(stars);
        condition.setTransferStaff(transferStaff);
        condition.setTaskKey(taskKey);
        condition.setTaskVersion(taskVersion);
        condition.setTaskNode(taskNode);

        PageParam<InspectWorkbenchConditionParam> pageParam = new PageParam<>();
        pageParam.setPageNum(pageNum);
        pageParam.setPageSize(pageSize);
        pageParam.setCondition(condition);
        return pageParam;
    }
}
